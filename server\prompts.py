def get_proposal_summary_prompt(text):
    """
    Returns the prompt for summarizing a proposal form, formatted with the provided text.
    """
    return (
        """The uploaded text is a completed proposal form for a personal indemnity quotation. Extract and return the following information as structured JSON—exactly two objects: 'proposal_summary' and 'insurance_history'. Carefully distinguish and count business partners, qualified staff (qualified assistants), and unqualified staff (unqualified assistants or other staff). For staff counts, do not duplicate values; for instance, if a person is listed as both a business partner and staff, only count them once in the appropriate category. Qualified staff typically have a university degree or regulatory body registration, while unqualified staff usually do not (e.g., kitchen staff, cleaners, security guards, messengers). Qualified assistants are also called qualified staff. The number of business partners is the count of all partners/principals/directors named. Staff other than typists and messengers fall under unqualified staff.

For staff counts, extract from tables if possible, otherwise from text. When a value refers to another document, set it to null.

Clarification for key fields:
- 'Name of Broker': company that mediates the reinsurance transaction between the insured and the reinsurance company.
- 'Proposer / Insured': the insured party being proposed for insurance.
- 'Name of reinsured / cedant': the insurance company or entity that will provide the insurance coverage.
- 'Breach of Authority': extension indicating coverage for breach of authority (yes/no). Do not confuse with employee dishonesty.
- 'Period of cover': the policy duration, usually 1 year unless otherwise stated.
- 'Estimated annual fee': the insured’s expected income for the next 12 months.
- Interpret Y/N as Yes/No.
- For checkboxes: checked = 'Yes', unchecked = 'No.'
- 'Limit of indemnity' and 'applicable excess/deductible' refer only to the policy being applied for; distinguish from prior/current policies. These must be returned as numbers (e.g., 100000, not text).
- 'Deductible/Excess Applicable' is a numeric value (e.g., 200000, not text).
- If the value for 'Deductible/Excess Applicable' is present and not the 'Excess/Deductible percentage', compute the missing percentage as (deductible / limit of indemnity), rounded to two decimal places. If the percentage is present but not the deductible, compute the deductible as (percentage * limit of indemnity), rounded to the nearest integer.
- If only one (percentage or value) for deductible/excess is present, compute the other if possible; otherwise, set to null.
- For the extension fields: Defamation Coverage (equivalent to 'libel and slander'), Loss of Documents, Errors and Omissions, Applicable Excess/Maximum Excess, Incoming/Outgoing partners, Dishonesty of employees, and Breach of Authority: if not present, set to null.
- If there is another extension requested for that can not be mapped to the extension above set the value of the field Uncovered extension to Yes, otherwise, null.
- For 'insurance_history': values such as declined/terminated/refused/claims-settled/claim-awareness must be extracted as Yes/No/null.
- The statement 'Deductible each and every claim to be borne by insured is limited to 1% of limit of indemnity, (Min Kshs 50,000)' is for reference only and does not impact extraction; it does not refer to the value of the Excess/ Deductible percentage.
- Do not misinterpret the "Excess" section as purely informational. Map all the checkboxes.
- Ensure all checkboxes are mapped to their respective value for all fields and extracted.
- For rectroactive cover field, its value is boolean, either yes or no.
- If a proposal form requests for retroactive cover or has a retoactive date present, tabulate how many months (typically 12 or 24 , round up or down the number of months to 12 or 24 )the cover is meant to go back from the date the proposal form is filled. The number of months should be filled in the retroactive period field. Otherwise, null.

For numeric ranges, select the lower value; if not possible, return null. If a field refers to another document or is not found, set to null.
Monetary fields must be output as integers (no commas, expand 'M' to millions). Percentages must be returned as floats (1% = 0.01). If a field includes comma-separated lists or names, replace commas with spaces. If insurance company is listed as 'XPA', use 'APA Insurance'. For any missing required field, set to null (not '-'). Ignore extra/unexpected data.

## Output Format
Return a valid JSON object with exactly two top-level objects: 'proposal_summary' and 'insurance_history'.

- Each object contains a 'headers' array (field names) and a 'values' array (with values matching header order).
- All field types must be explicit: use numbers for monetary/staff count fields, strings for names/occupation/period, null for missing or unavailable values, and 'Yes'/'No' where appropriate.
- Include all required headers, matching the order in the schema example below, even if some values are null.
- Format monetary values as plain integers without commas or text, expanding 'M' notation if needed. Percentages must be decimals (e.g., 1% to 0.01).
- For any comma-separated text in name or list fields, replace commas with spaces.
- For computed fields (e.g., deductible/excess percentage or value), round decimals to two decimal places.
- Only output the fields listed in the schema; ignore all others.
- Set any field that refers to another document, is not found, or cannot be computed to null.
- Deduplicate and count staff/business partners according to the rules above.
- For extraction ambiguities or duplicated fields, deduplicate and handle errors by setting only one value or null.

### Example JSON Output:
{
  "proposal_summary": {
    "headers": [
      "Name of Proposer / Insured",
      "Name of Cedant / Reinsured",
      "Name of Broker",
      "Period of Cover",
      "Number of Business Partners",
      "Number of Qualified staff",
      "Number of Unqualified staff",
      "Estimated Annual Income",
      "Limit of Indemnity (Cover Limit)",
      "Occupation of the Insured",
      "Deductible/Excess Applicable",
      "Excess/ Deductible percentage",
      "Retroactive cover",
      "Retroactive period",
      "Defamation Coverage",
      "Loss of Documents",
      "Incoming/Outgoing partners",
      "Dishonesty of employees",
      "Breach of Authority",
      "Uncovered extension"
    ],
    "values": [
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ]
  },
  "insurance_history": {
    "headers": [
      "Previous/current insurance",
      "insurance company",
      "Declined previous insurance proposal",
      "Terminated/ refused insurance renewal",
      "Claims setted/ outstanding",
      "Claim circumstance awareness"
    ],
    "values": [
      "",
      "",
      "",
      "",
      "",
      ""
    ]
  }
}"""
        f"{text}"
    )

def get_financial_summary_prompt(text):
    """
    Returns a detailed prompt for summarizing financial text, formatted with the provided text.
    """
    return (
        "Please summarize the following text, assessing the financial soundness of the firm or individual.\n\n"
        "- Summarize annual profit or loss for each available year, ensuring a separate entry per year.\n"
        "- If the field \"Profit (loss) for the year before tax\" is present in the extracted text, it simply means the value following it is the profit or loss for the year. "
        "- Extract the financial summary for as many years as are available in the text, with a minimum of 3 years if possible. If fewer than three years are present, include as many as are available. "
        "Extract and summarize all available financial information, even if it appears in different parts of the text.\n"
        "Below is an example of a financial statement:STATEMENT OF COMPREHENSIVE INCOME 2022 2021 Note KShs KShs Income Business income 4,609,645 3,092,164 Other income 259,810 350,000 4,869,455 3,442,164 Administrative expenses 1 (4,709,648) (3,225,230) Establishment expenses 2 (1,474,415) (2,172,773) Finance expenses 3 (32,508) (65,997) Total expenses (6,216,571) (5,464,000) Profit (loss) for the year before tax (1,347,116) (2,021,836) Tax (45,429) (105,000) - Profit (Loss) for the year after tax (1,392,545) (2,126,836) \n"
        "In the example above, the profit (loss) for the year before tax is (1,347,116) and the profit (loss) for the year after tax is (1,392,545). The profit (loss) for the year before tax is negative and the profit (loss) for the year after tax is positive. Therefore, the firm is profitable.\n"
        "In the example aove the - symbol that appears after the tax is a minus sign and not a dash. The - symbol is used to indicate a negative value.\n"
        "Using this example, reason ou whether the - symbols that may appear in the text are a minus sign or a dash. If it truly is a minus sign, then the value should be negative. If it truly is a dash, then the value should be positive.\n"
        "- Only extract numerical values that are clear and parseable. If a value is malformed or cannot be parsed, leave it as an empty string.\n"
        "- If a particular field (e.g., revenue, profit_before_tax) is missing for a year, set its value to an empty string.\n"
        "- Ensure entries in the output array are sorted in chronological order (earliest fiscal year first).\n"
        "- Numbers must use commas as thousands separators (e.g., 100,000 instead of 100000).\n"
        "- No numbers should be in brackets. If a number is in brackets, extract the number without the brackets.\n"
        "- Return valid JSON output only. Do not include any explanations or additional text. There should be no brackets in the output.\n\n"
        "## Output Format\n\n"
        "Return the output as valid JSON in the following schema:\n\n"
        "{\n"
        '  "financial_summary": [\n'
        "    {\n"
        '      "revenue": "<string - number with commas, or empty string if missing, no brackets,numeric datatype>",\n'
        '      "operating_profit": "<string - number with commas, or empty string if missing, no brackets,numeric datatype>",\n'
        '      "profit_before_tax": "<string - number with commas, or empty string if missing, no brackets,numeric datatype>",\n'
        '      "profit_after_tax": "<string - number with commas, or empty string if missing, no brackets,numeric datatype>",\n'
        '      "fiscal_year": "<string - year, or empty string if missing>"\n'
        "    }\n"
        "    // ...one entry per available year\n"
        "  ]\n"
        "}\n\n"
        f"Text:\n{text}"
    )


def get_proposal_extraction_prompts():
    """
    Returns the (system, user) prompt tuple for extracting text from proposal form images.
    """
    system_prompt = (
        "You are an OCR assistant specialized in extracting text from proposal forms. "
        "Focus on identifying form fields, checkboxes, and their values."
        "Ensure that the tables are extracted correctly."
    )
    user_prompt = "Extract all text from this proposal form image. Pay special attention to form fields and their values."
    return system_prompt, user_prompt

def get_financial_extraction_prompts():
    """
    Returns the (system, user) prompt tuple for extracting text from financial statement images.
    """
    system_prompt = (
        "You are an OCR assistant specialized in extracting text from financial statements. "
        "Focus on identifying numerical values, tables, and financial data."
        "Ensure that the tables are extracted correctly. Match the columnms and rows to their respective values"
    )
    user_prompt = "Extract all text from this financial statement image. Pay special attention to numerical values and tables."
    return system_prompt, user_prompt 